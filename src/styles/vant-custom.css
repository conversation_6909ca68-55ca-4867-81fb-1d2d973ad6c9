/* Vant组件深色主题自定义样式 */

/* ActionSheet 动作面板样式 */
.van-action-sheet {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

.van-action-sheet__header {
  background-color: #1e1e1e !important;
  border-bottom: 1px solid #333333 !important;
}

.van-action-sheet__title {
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 500 !important;
}

.van-action-sheet__close {
  color: #8f8f8f !important;
}

.van-action-sheet__close:hover {
  color: #ffffff !important;
}

.van-action-sheet__item {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-bottom: 1px solid #333333 !important;
  font-size: 16px !important;
  padding: 16px 20px !important;
}

.van-action-sheet__item:hover {
  background-color: #2a2a2a !important;
}

.van-action-sheet__item:active {
  background-color: #333333 !important;
}

.van-action-sheet__cancel {
  background-color: #1e1e1e !important;
  color: #8f8f8f !important;
  border-top: 8px solid #121212 !important;
}

.van-action-sheet__cancel:hover {
  background-color: #2a2a2a !important;
  color: #ffffff !important;
}

/* 遮罩层样式 */
.van-overlay {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

/* 选中状态样式 */
.van-action-sheet__item--selected {
  color: #eaae36 !important;
  background-color: #2a2a2a !important;
}

.van-action-sheet__item--selected::after {
  content: '✓';
  float: right;
  color: #eaae36 !important;
  font-weight: bold;
}
