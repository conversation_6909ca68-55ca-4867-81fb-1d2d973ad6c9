# K线图布局优化 - 空白预测区内置

## 🎯 布局调整说明

根据您的要求，已将空白预测区调整为K线图内部的一部分，而不是外部独立区域。

### 📊 新的布局结构

```
┌─────────────────────────────────────────────────────────┐
│                    黑色K线图背景                          │
│  ┌─────────────────────┬─────────────────────────────┐   │
│  │                     │                             │   │
│  │    K线区域          │        空白预测区            │   │
│  │   (左侧 2/3)        │       (右侧 1/3)            │   │
│  │                     │                             │   │
│  │  ████ ████ ████     │                             │   │
│  │  ████ ████ ████     │     ～～～实时价格线～～～    │   │
│  │  ████ ████ ████     │                             │   │
│  │                     │                             │   │
│  └─────────────────────┴─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 1. 区域划分
```javascript
// 总图表宽度
const totalChartWidth = width - 80; // 为右侧价格标签留空间

// K线区域占2/3，预测区占1/3
const klineWidth = Math.floor(totalChartWidth * 2 / 3);
const predictionZoneWidth = totalChartWidth - klineWidth;
const predictionZoneLeft = klineWidth;
```

### 2. K线绘制范围
- **绘制区域**：左侧2/3区域
- **蜡烛宽度**：基于klineWidth计算
- **蜡烛间距**：基于klineWidth分布

### 3. 预测区设计
- **位置**：右侧1/3区域
- **背景**：半透明白色覆盖
- **边界**：虚线分隔
- **标签**：居中显示"预测区"

### 4. 网格线系统
- **水平网格线**：延伸到整个图表区域（包括预测区）
- **垂直网格线**：只在K线区域显示
- **当前价格线**：延伸到整个图表区域

### 5. 实时价格曲线
- **显示区域**：预测区内
- **时间跨度**：90秒（1分30秒）
- **颜色**：青色 `#00bcd4`
- **更新频率**：每秒一次

## 📈 功能分布

### 左侧K线区域 (2/3)
✅ **K线蜡烛图**：历史价格数据  
✅ **买入点标记**：交易下单位置  
✅ **垂直网格线**：时间刻度  
✅ **交易标记**：精美的买入点图标  

### 右侧预测区 (1/3)
✅ **实时价格曲线**：1分30秒价格走势  
✅ **当前价格点**：实时价格标记  
✅ **空白背景**：预测区域标识  
✅ **边界虚线**：区域分隔  

### 全图表元素
✅ **水平网格线**：价格刻度线  
✅ **当前价格线**：黄色虚线  
✅ **价格标签**：右侧价格显示  

## 🎨 视觉效果

### 颜色方案
- **K线区域**：黑色背景 `#0a0a0a`
- **预测区背景**：半透明白色 `rgba(255, 255, 255, 0.03)`
- **边界线**：灰色虚线 `#444`
- **实时曲线**：青色 `#00bcd4`
- **当前价格线**：黄色 `#ffeb3b`

### 布局比例
- **K线区域**：66.7% (2/3)
- **预测区域**：33.3% (1/3)
- **价格标签区**：80px 固定宽度

## 🔄 数据流向

### 历史数据 → K线区域
1. 模拟K线数据生成
2. 在左侧2/3区域绘制蜡烛图
3. 显示买入点标记

### 实时数据 → 预测区域
1. 每秒记录价格历史
2. 在右侧1/3区域绘制价格曲线
3. 显示当前价格点

### 全局元素 → 整个图表
1. 水平网格线延伸全图
2. 当前价格线延伸全图
3. 价格标签显示在右侧

## 🎯 用户体验

### 视觉层次
1. **主要关注**：左侧K线图（历史数据）
2. **次要关注**：右侧预测区（实时走势）
3. **辅助信息**：网格线和价格标签

### 交互逻辑
1. **观察历史**：在K线区域分析趋势
2. **实时跟踪**：在预测区观察当前走势
3. **下单决策**：基于两个区域的信息
4. **结果验证**：在预测区看到价格发展

## 📱 响应式适配

### 不同屏幕尺寸
- **大屏幕**：2/3 + 1/3 比例保持
- **中等屏幕**：自动调整宽度
- **小屏幕**：保持比例，缩小整体

### 动态调整
- **图表宽度**：根据容器自适应
- **区域比例**：始终保持2:1
- **元素大小**：按比例缩放

现在的布局完全符合您的要求：空白预测区在K线图的黑色背景内部，左边2/3是K线，右边1/3是预测区！
