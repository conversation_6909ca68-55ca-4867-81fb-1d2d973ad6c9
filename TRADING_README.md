# 二元期权交易页面 (重新设计版本)

## 功能概述

这是一个完全重新设计的模拟二元期权交易平台，参考了 binary.perps.cfd 的专业设计风格，包含以下主要功能：

### 1. 实时 K 线图

- 使用 `lightweight-charts` 库显示 BTC-USD 价格走势
- 实时更新价格数据（模拟数据）
- 支持多种时间周期选择（31 秒、60 秒、2 分钟、5 分钟）
- 绿色蜡烛表示上涨，红色蜡烛表示下跌

### 2. 交易功能

- **看涨/看跌交易**：用户可以选择价格上涨或下跌方向
- **交易金额设置**：支持自定义金额或快速选择预设金额
- **收益率显示**：实时显示预期收益（默认 1.75 倍）
- **交易限制**：在倒计时最后 30 秒内禁止交易

### 3. 下单方式问题说明

根据您提供的图片说明，实现了以下规则：

#### 操作时间限制

- 每局下注的第 31 秒至第 60 秒时间段内，用户无法下注
- 需要等待当前局结束后，才能进行下一局投注
- 页面显示实时倒计时，红色表示禁止交易时间

#### 投资/投票机制

- 后台可根据当前计算数据点进行"一键投票"
- 系统根据价格点数据进行走势分析
- 提高平台胜率的机制

#### 手续费说明

- 用户下注产生 3%交易手续费
- VIP 等级规则进行分层分发
- 非 VIP 用户最高需要缴纳 500 Lucky 币手续费

### 4. 交易历史

- 显示最近的交易记录
- 包含时间、方向、金额、结果、盈亏信息
- 盈利显示绿色，亏损显示红色

### 5. 实时功能

- **价格更新**：每 2 秒更新一次价格数据
- **倒计时器**：显示当前交易周期剩余时间
- **交易状态**：实时显示是否可以交易

## 技术实现

### 依赖库

- `react`: 前端框架
- `tailwindcss`: 样式框架
- 自定义 Canvas K 线图组件（替代 lightweight-charts 以避免兼容性问题）

### 核心组件

- `Trading.jsx`: 主要交易页面组件
- 实时数据生成和更新逻辑
- 倒计时和交易限制逻辑

### 数据模拟

- K 线数据：基于随机算法生成真实的 OHLC 数据
- 价格更新：模拟真实市场的价格波动
- 交易历史：预设的模拟交易记录

## 使用方法

1. 访问 `/trading` 路径
2. 观察实时 K 线图和价格变化
3. 设置交易金额
4. 选择看涨或看跌方向
5. 在允许的时间内点击交易按钮
6. 查看交易历史记录

## 注意事项

- 所有数据均为模拟数据，仅用于演示
- 交易功能仅为 UI 展示，不涉及真实资金
- 倒计时和交易限制按照二元期权规则实现
- 页面支持响应式设计，适配移动端

## 页面截图说明

页面实现了您图片中展示的所有核心功能：

- 左侧：实时 K 线图显示
- 右侧：交易面板，包含金额设置、收益显示、交易按钮
- 顶部：下单方式问题的详细说明
- 底部：交易历史记录表格
- 倒计时器：显示当前交易周期状态

所有功能都使用模拟数据，可以安全地进行测试和演示。
